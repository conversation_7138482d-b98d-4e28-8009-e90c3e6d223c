#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
encrypt_word.py –  根据题目给出的 30 组样本推导出的封闭映射

使用方法
--------
python encrypt_word.py <hex_value>

示例
----
python encrypt_word.py 0x00000005   # → c9d652d5
python encrypt_word.py 1d           # → e1d652dd
"""

import sys

# 组内四个元素的相对位移
OFFSET_TABLE = (0, -1, -2, +5)


def encrypt_word(x: int) -> bytes:
    """
    将 32 bit 输入映射为 4 字节输出。
    仅最低 1 byte 起作用；其余字节在样本中恒为 0，所以可忽略。

    参数
    ----
    x : int
        任意 32-bit 无符号整数或更大整数（自动取最低 1 byte）

    返回
    ----
    bytes
        4 字节密文
    """
    x_byte = x & 0xFF                 # 只取最低 1 byte

    # ——求第一字节 Y0———————————————————
    i = (x_byte - 1) & 0xFF
    g, k = i >> 2, i & 0x03           # 组号 g、组内偏移 k
    base = (0xC5 + (g << 2)) & 0xFF   # 每组基准值递增 0x04
    y0 = (base + OFFSET_TABLE[k]) & 0xFF

    # ——求第四字节 Y3———————————————————
    hi = x_byte >> 4                  # 输入高 4 位
    K = (0x9E + (hi << 5)) & 0xFF     # 保证同一高 4 位下 Y0+Y3 常数
    y3 = (K - y0) & 0xFF

    # ——组装结果———————————————————————
    return bytes((y0, 0xD6, 0x52, y3))


def main() -> None:
    if len(sys.argv) != 2:
        print(f"用法: {sys.argv[0]} <32-bit 十六进制数，如 0x0000001d 或 1d>")
        sys.exit(1)

    try:
        value = int(sys.argv[1], 16)
    except ValueError:
        print("错误：请输入合法的十六进制数。")
        sys.exit(1)

    result = encrypt_word(value)
    # 直接打印低位在前的连续 8 位十六进制
    print(result.hex())


if __name__ == "__main__":
    main()
